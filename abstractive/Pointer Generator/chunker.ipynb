{"cells": [{"cell_type": "markdown", "id": "c4f4f89a", "metadata": {}, "source": ["### Script to chunk text files\n", "\n", "Each chunk would have its own text file"]}, {"cell_type": "code", "execution_count": null, "id": "d855afea", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import nltk\n", "import os\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *"]}, {"cell_type": "code", "execution_count": null, "id": "4bc23a54", "metadata": {}, "outputs": [], "source": ["output_path = \"./chunked/\"\n", "if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": null, "id": "a446e900", "metadata": {}, "outputs": [], "source": ["names, data_source, data_summary = get_summary_data(\"IN\", \"test\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))"]}, {"cell_type": "code", "execution_count": null, "id": "366ba48f", "metadata": {}, "outputs": [], "source": ["output = []\n", "for i in range(len(data_source)):\n", "    name = names[i]\n", "    doc = data_source[i]\n", "    wc = doc.split(\" \")\n", "    print(str(i) + \": \" + name)\n", "    nested = nest_sentences(doc,395)\n", "    \n", "    for i in range(len(nested)):\n", "        path = output_path + name[:-4] + \"_\" + str(i) + \".txt\"\n", "        print(path)\n", "        file = open(path,'w')\n", "        file.write(nested[i])\n", "        file.close()\n", "    \n", "print(output)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}}, "nbformat": 4, "nbformat_minor": 5}