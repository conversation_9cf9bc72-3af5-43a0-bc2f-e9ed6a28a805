{"cells": [{"cell_type": "markdown", "id": "ee01a304", "metadata": {}, "source": ["### Script to combine summaries generated from each chunk for each file\n", "\n", "Change the input and output path according to requirements"]}, {"cell_type": "code", "execution_count": null, "id": "02fdfddd", "metadata": {}, "outputs": [], "source": ["import os\n", "input_path = \"./chunked/\"\n", "output_path = \"./result/\"\n", "if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": null, "id": "7bafb697", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *"]}, {"cell_type": "code", "execution_count": null, "id": "ed178de7", "metadata": {}, "outputs": [], "source": ["# Reading every chunk and combining summaries to get the final summaries\n", "from collections import defaultdict\n", "\n", "path = input_path\n", "all_files = glob.glob(path + \"/*.txt\")\n", "\n", "chunk_sum = defaultdict(list)\n", "\n", "for filename in all_files:\n", "    with open(filename, 'r') as f: \n", "        p = filename.rfind(\"/\")\n", "        name = (filename[p+1:])\n", "        chunk_number = int(name[name.rfind(\"_\")+1:name.rfind(\".\")])\n", "        name = name[:name.rfind(\"_\")] + \".txt\"\n", "        chunk_sum[name].append((chunk_number, f.read()))\n", "#         print(chunk_number)\n", "#         break"]}, {"cell_type": "code", "execution_count": null, "id": "e42a54d2", "metadata": {}, "outputs": [], "source": ["len(chunk_sum)"]}, {"cell_type": "code", "execution_count": null, "id": "610b7898", "metadata": {}, "outputs": [], "source": ["dict_names = get_req_len_dict(\"IN\", \"test\")   "]}, {"cell_type": "code", "execution_count": null, "id": "cbf4995d", "metadata": {}, "outputs": [], "source": ["\n", "# Writing the resultant summaries to text files\n", "for filename in chunk_sum.keys():\n", "#     print(filename)\n", "#     print(chunk_sum[filename])\n", "    sorted_chunks = (sorted(chunk_sum[filename], key=lambda x: x[0]))\n", "    summary = ''\n", "    for i in sorted_chunks:\n", "        summary = summary + i[1]\n", "#     print(summary)\n", "    generated_length = len(summary.split(\" \"))\n", "    required_length = dict_names[filename]\n", "    if generated_length > required_length:\n", "        summary = summary.split(\" \")\n", "        summary = summary[:required_length]\n", "        summary = \" \".join(summary)\n", "    path = output_path + filename\n", "    file = open(path,'w')\n", "    file.write(summary)\n", "    file.close()\n", "#     break"]}], "metadata": {"kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}