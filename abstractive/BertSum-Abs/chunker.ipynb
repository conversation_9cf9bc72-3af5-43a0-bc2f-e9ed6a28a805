{"cells": [{"cell_type": "markdown", "id": "111743d0", "metadata": {"id": "111743d0"}, "source": ["### Script to chunk and save every file. Every line in the output file is a chunk.  "]}, {"cell_type": "code", "execution_count": null, "id": "6f05a1f8", "metadata": {"id": "6f05a1f8"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN - IN-Abs, UK-UK-Abs, N2-IN-Ext \n", "output_path = \"./output/\""]}, {"cell_type": "code", "execution_count": null, "id": "82ea4352", "metadata": {"id": "82ea4352"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import os\n", "import nltk\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "names, data_source, data_summary = get_summary_data(dataset, \"test\")"]}, {"cell_type": "code", "execution_count": null, "id": "74b25143", "metadata": {"id": "74b25143"}, "outputs": [], "source": ["import os\n", "if not os.path.exists(output_path + './'+ dataset +'_chunked/'):\n", "    os.makedirs(output_path + './'+ dataset +'_chunked/')"]}, {"cell_type": "code", "execution_count": null, "id": "0238b231", "metadata": {"id": "0238b231"}, "outputs": [], "source": ["for i in range(len(data_source)):\n", "    doc = data_source[i].replace(\"\\n\", \" \")\n", "    chunks = nest_sentences(doc, 512)\n", "    save = \"\\n\".join(chunks)\n", "    path = output_path +  \"./\"+ dataset +\"_chunked/\" + names[i]\n", "    file = open(path,'w')\n", "    file.write(save)\n", "    file.close()\n", "#     break"]}], "metadata": {"colab": {"name": "chunker.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}