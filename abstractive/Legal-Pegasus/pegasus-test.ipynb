{"cells": [{"cell_type": "markdown", "id": "8a842a0a", "metadata": {"id": "8a842a0a"}, "source": ["### Script to generate summaries using chunking based Pegasus approach"]}, {"cell_type": "code", "execution_count": null, "id": "cb972436", "metadata": {"id": "cb972436"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN - IN-Abs, UK-UK-Abs, N2-IN-Ext\n", "output_path = \"./output/\""]}, {"cell_type": "code", "execution_count": null, "id": "1a83915a", "metadata": {"id": "1a83915a"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "import os\n", "import nltk"]}, {"cell_type": "code", "execution_count": null, "id": "049e7a6f", "metadata": {"id": "049e7a6f"}, "outputs": [], "source": ["if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": null, "id": "0591966a", "metadata": {"id": "0591966a"}, "outputs": [], "source": ["#Reading the test documents\n", "names, data_source, data_summary = get_summary_data(dataset, \"test\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))\n", "len_dic = dict_names = get_req_len_dict(dataset, \"test\")  "]}, {"cell_type": "code", "execution_count": null, "id": "14b68b70", "metadata": {"id": "14b68b70"}, "outputs": [], "source": ["device = \"cuda:1\""]}, {"cell_type": "code", "execution_count": null, "id": "0596cefd", "metadata": {"id": "0596cefd"}, "outputs": [], "source": ["# Loading Model and tokenizer\n", "from transformers import AutoTokenizer, AutoModelForSeq2SeqLM\n", "from transformers import PegasusForConditionalGeneration, PegasusTokenizer, Trainer, TrainingArguments\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(\"nsi319/legal-pegasus\")  \n", "model = AutoModelForSeq2SeqLM.from_pretrained(\"nsi319/legal-pegasus\").to(device)\n"]}, {"cell_type": "code", "execution_count": null, "id": "49734483", "metadata": {"id": "49734483"}, "outputs": [], "source": ["def summerize(text, max_len, min_len):\n", "    '''\n", "    Function to generate summary using Pegasus\n", "    input:  nested_sentences - chunks\n", "            max_l - Maximum length\n", "            min_l - Minimum length\n", "    output: document summary\n", "    '''\n", "    try:\n", "        input_tokenized = tokenizer.encode(text, return_tensors='pt',max_length=512,truncation=True).to(device)\n", "        summary_ids = model.generate(input_tokenized,\n", "                                          num_beams=9,\n", "                                          length_penalty=0.1,\n", "                                          min_length=min_len,\n", "                                          max_length=max_len,\n", "                                    )\n", "        summary = [tokenizer.decode(g, skip_special_tokens=True, clean_up_tokenization_spaces=False) for g in summary_ids][0]\n", "        return summary\n", "    except:\n", "        return \"\""]}, {"cell_type": "code", "execution_count": null, "id": "e7f05633", "metadata": {"id": "e7f05633"}, "outputs": [], "source": ["def summerize_doc(nested_sentences, p):\n", "    '''\n", "    Function to generate summary using chunking based Pegasus\n", "    input:  nested_sentences - chunks\n", "            p - Number of words in summaries per word in the document\n", "    output: document summary\n", "    '''\n", "    device = 'cuda'\n", "    result = []\n", "    for nested in nested_sentences:\n", "        l = int(p * len(nested.split(\" \")))\n", "        max_len = l\n", "        min_len = l-5\n", "        result.append(summerize(nested, max_len, min_len))\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "fd0385a1", "metadata": {"id": "fd0385a1"}, "outputs": [], "source": ["done_files = glob.glob(output_path + \"*.txt\")\n", "done_files = [i[i.rfind(\"/\")+1:] for i in done_files]"]}, {"cell_type": "code", "execution_count": null, "id": "0da2186e", "metadata": {"id": "0da2186e"}, "outputs": [], "source": ["# main loop to generate and save summaries of each document in the test dataset\n", "for i in range(len(data_source)):\n", "    done_files = glob.glob(output_path + \"*.txt\")\n", "    done_files = [i[i.rfind(\"/\")+1:] for i in done_files]\n", "    name = names[i]\n", "    if name in done_files:continue\n", "    doc = data_source[i]\n", "    input_len = len(doc.split(\" \"))\n", "    req_len = dict_names[name]\n", "    print(str(i) + \": \" + name +  \" - \" + str(input_len) + \" : \" + str(req_len), end = \", \")\n", "    \n", "    nested = nest_sentences(doc,512)\n", "    p = float(req_len/input_len)\n", "    print(p)\n", "    abs_summ = summerize_doc(nested,p)\n", "    abs_summ = \" \".join(abs_summ)\n", "    print(len((abs_summ.split(\" \"))))\n", "    \n", "    if len(abs_summ.split(\" \")) > req_len:\n", "        abs_summ = abs_summ.split(\" \")\n", "        abs_summ = abs_summ[:req_len]\n", "        abs_summ = \" \".join(abs_summ)\n", "    print(len((abs_summ.split(\" \"))))\n", "    path = output_path + name\n", "    file = open(path,'w')\n", "    file.write(abs_summ)\n", "    file.close()\n", "#     break"]}, {"cell_type": "code", "execution_count": null, "id": "33df9665", "metadata": {"id": "33df9665"}, "outputs": [], "source": [""]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}, "colab": {"name": "pegasus-test.ipynb", "provenance": [], "collapsed_sections": []}}, "nbformat": 4, "nbformat_minor": 5}