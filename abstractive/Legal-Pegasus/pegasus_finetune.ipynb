{"cells": [{"cell_type": "markdown", "id": "6cf3e374", "metadata": {"id": "6cf3e374"}, "source": ["### Script to finetune pegasus model\n", "source https://gist.github.com/jiahao87/50cec29725824da7ff6dd9314b53c4b3\n", "\n", "add the path to the fine tuning data excel to filename variable"]}, {"cell_type": "code", "execution_count": null, "id": "670aac2b", "metadata": {"id": "670aac2b"}, "outputs": [], "source": ["import pandas as pd\n", "\n", "filename = \"filename\"\n", "\n", "df = pd.read_excel(filename,index_col=0)\n", "df.rename(columns = {'data':'source', 'summary':'target'}, inplace = True)\n", "len(df)"]}, {"cell_type": "code", "execution_count": null, "id": "ad95a5e7", "metadata": {"id": "ad95a5e7"}, "outputs": [], "source": ["from transformers import PegasusForConditionalGeneration, PegasusTokenizer, Trainer, TrainingArguments\n", "import torch\n", "from transformers import AutoTokenizer, AutoModelForSeq2SeqLM\n", "\n", "\n", "class PegasusDataset(torch.utils.data.Dataset):\n", "    def __init__(self, encodings, labels):\n", "        self.encodings = encodings\n", "        self.labels = labels\n", "    def __getitem__(self, idx):\n", "        item = {key: torch.tensor(val[idx]) for key, val in self.encodings.items()}\n", "        item['labels'] = torch.tensor(self.labels['input_ids'][idx])  # torch.tensor(self.labels[idx])\n", "        return item\n", "    def __len__(self):\n", "        return len(self.labels['input_ids'])  # len(self.labels)\n", "\n", "      \n", "def prepare_data(model_name, \n", "                 train_texts, train_labels, \n", "                 val_texts=None, val_labels=None, \n", "                 test_texts=None, test_labels=None):\n", "  \"\"\"\n", "  Prepare input data for model fine-tuning\n", "  \"\"\"\n", "  tokenizer = AutoTokenizer.from_pretrained(model_name)\n", "\n", "  prepare_val = False if val_texts is None or val_labels is None else True\n", "  prepare_test = False if test_texts is None or test_labels is None else True\n", "\n", "  def tokenize_data(texts, labels):\n", "    encodings = tokenizer(texts, truncation=True, padding=True, max_length = 512)\n", "    decodings = tokenizer(labels, truncation=True, padding=True, max_length = 256)\n", "    dataset_tokenized = PegasusDataset(encodings, decodings)\n", "    return dataset_tokenized\n", "\n", "  train_dataset = tokenize_data(train_texts, train_labels)\n", "  val_dataset = tokenize_data(val_texts, val_labels) if prepare_val else None\n", "  test_dataset = tokenize_data(test_texts, test_labels) if prepare_test else None\n", "\n", "  return train_dataset, val_dataset, test_dataset, tokenizer\n", "\n", "\n", "def prepare_fine_tuning(model_name, tokenizer, train_dataset, val_dataset=None, freeze_encoder=False, output_dir='./results'):\n", "  \"\"\"\n", "  Prepare configurations and base model for fine-tuning\n", "  \"\"\"\n", "  torch_device = 'cuda' if torch.cuda.is_available() else 'cpu'\n", "  model = PegasusForConditionalGeneration.from_pretrained(model_name).to(torch_device)\n", "\n", "  if freeze_encoder:\n", "    for param in model.model.encoder.parameters():\n", "      param.requires_grad = False\n", "\n", "  if val_dataset is not None:\n", "    training_args = TrainingArguments(\n", "      output_dir=output_dir,           # output directory\n", "      num_train_epochs=2,           # total number of training epochs\n", "      per_device_train_batch_size=1,   # batch size per device during training, can increase if memory allows\n", "      per_device_eval_batch_size=1,    # batch size for evaluation, can increase if memory allows\n", "      save_steps=500,                  # number of updates steps before checkpoint saves\n", "      save_total_limit=5,              # limit the total amount of checkpoints and deletes the older checkpoints\n", "      evaluation_strategy='steps',     # evaluation strategy to adopt during training\n", "      eval_steps=100,                  # number of update steps before evaluation\n", "      warmup_steps=500,                # number of warmup steps for learning rate scheduler\n", "      weight_decay=0.01,               # strength of weight decay\n", "      logging_dir='./logs',            # directory for storing logs\n", "      logging_steps=100,\n", "    )\n", "\n", "    trainer = Trainer(\n", "      model=model,                         # the instantiated 🤗 Transformers model to be trained\n", "      args=training_args,                  # training arguments, defined above\n", "      train_dataset=train_dataset,         # training dataset\n", "      eval_dataset=val_dataset,            # evaluation dataset\n", "      tokenizer=tokenizer\n", "    )\n", "\n", "  else:\n", "    training_args = TrainingArguments(\n", "      output_dir=output_dir,           # output directory\n", "      num_train_epochs=2,           # total number of training epochs\n", "      per_device_train_batch_size=1,   # batch size per device during training, can increase if memory allows\n", "      save_steps=500,                  # number of updates steps before checkpoint saves\n", "      save_total_limit=5,              # limit the total amount of checkpoints and deletes the older checkpoints\n", "      warmup_steps=500,                # number of warmup steps for learning rate scheduler\n", "      weight_decay=0.01,               # strength of weight decay\n", "      logging_dir='./logs',            # directory for storing logs\n", "      logging_steps=100,\n", "    )\n", "\n", "    trainer = Trainer(\n", "      model=model,                         # the instantiated 🤗 Transformers model to be trained\n", "      args=training_args,                  # training arguments, defined above\n", "      train_dataset=train_dataset,         # training dataset\n", "      tokenizer=tokenizer\n", "    )\n", "\n", "  return trainer"]}, {"cell_type": "code", "execution_count": null, "id": "086e44a4", "metadata": {"colab": {"background_save": true}, "id": "086e44a4"}, "outputs": [], "source": ["train_texts, train_labels = (list(df['source'])), (list(df['target']))\n", "  \n", "model_name = 'nsi319/legal-pegasus'\n", "train_dataset, _, _, tokenizer = prepare_data(model_name, train_texts, train_labels)\n", "trainer = prepare_fine_tuning(model_name, tokenizer, train_dataset)\n", "trainer.train()"]}, {"cell_type": "code", "execution_count": null, "id": "PtPGCoHxfXGv", "metadata": {"colab": {"background_save": true}, "id": "PtPGCoHxfXGv"}, "outputs": [], "source": ["import os\n", "if not os.path.exists('./ouput_model/'):\n", "    os.makedirs('./ouput_model/')\n", "trainer.model.save_pretrained(\"./ouput_model/\")"]}, {"cell_type": "code", "execution_count": null, "id": "h6qzOTIVhQVM", "metadata": {"colab": {"background_save": true}, "id": "h6qzOTIVhQVM"}, "outputs": [], "source": ["!zip -r ouput_model.zip ./ouput_model/"]}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "machine_shape": "hm", "name": "pegasus_finetune.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}}, "nbformat": 4, "nbformat_minor": 5}