{"cells": [{"cell_type": "markdown", "id": "6cf3e374", "metadata": {"id": "6cf3e374"}, "source": ["### Script to generate summaries using Legal-LED model\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "63ad1e2b", "metadata": {}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN - IN-Abs, UK-UK-Abs, N2-IN-Ext\n", "output_path = \"./output/\""]}, {"cell_type": "code", "execution_count": null, "id": "e2895caf", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "import os\n", "import nltk"]}, {"cell_type": "code", "execution_count": null, "id": "7d7a6d73", "metadata": {}, "outputs": [], "source": ["if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": null, "id": "af7086d5", "metadata": {}, "outputs": [], "source": ["#Reading the test documents\n", "names, data_source, data_summary = get_summary_data(dataset, \"test\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))\n", "len_dic = dict_names = get_req_len_dict(dataset, \"test\")"]}, {"cell_type": "code", "execution_count": null, "id": "07a2744e", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import random\n", "import nltk\n", "nltk.download('punkt')\n", "\n", "from IPython.display import display, HTML\n", "import torch\n", "import datasets\n", "from datasets import load_dataset, load_metric, Dataset\n", "from transformers import AutoTokenizer\n", "from transformers import AutoModelForSeq2SeqLM\n", "from transformers import LEDTokenizer, LEDForConditionalGeneration\n", "from transformers import Seq2SeqTrainer, Seq2SeqTrainingArguments"]}, {"cell_type": "code", "execution_count": null, "id": "3f97b9a4", "metadata": {}, "outputs": [], "source": ["model_name = \"nsi319/legal-led-base-16384\"\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)"]}, {"cell_type": "code", "execution_count": null, "id": "7721acec", "metadata": {}, "outputs": [], "source": ["encoder_max_length = 1024*16\n", "decoder_max_length = 1024"]}, {"cell_type": "code", "execution_count": null, "id": "e2667bb4", "metadata": {}, "outputs": [], "source": ["led = AutoModelForSeq2SeqLM.from_pretrained(model_name, gradient_checkpointing=True, use_cache=False)\n", "# set generate hyperparameters\n", "led.config.num_beams = 2\n", "led.config.max_length = decoder_max_length\n", "led.config.min_length = 256\n", "led.config.early_stopping = True\n", "led.config.no_repeat_ngram_size = 4\n"]}, {"cell_type": "code", "execution_count": null, "id": "8ebe2023", "metadata": {}, "outputs": [], "source": ["led = AutoModelForSeq2SeqLM.from_pretrained(\"./final_model/IN_model\", use_cache=False)\n", "led = led.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "id": "cd35ada2", "metadata": {}, "outputs": [], "source": ["import torch\n", "led.eval()\n", "\n", "def generate_summary_gpu(input_text, l):\n", "\tinputs = tokenizer(\n", "\t\tinput_text,\n", "\t\tpadding=\"max_length\",\n", "\t\ttruncation=True,\n", "\t\tmax_length=encoder_max_length,\n", "\t\treturn_tensors=\"pt\",\n", "\t)\n", "\tinput_ids = inputs.input_ids.to(led.device)\n", "\tattention_mask = inputs.attention_mask.to(led.device)\n", "\tglobal_attention_mask = torch.zeros_like(attention_mask)\n", "\t# put global attention on <s> token\n", "\tglobal_attention_mask[:, 0] = 1\n", "\tl = max(l,256)\n", "\tl = min(l,1024)\n", "\toutputs = led.generate(input_ids, attention_mask=attention_mask, global_attention_mask=global_attention_mask, max_length = l, num_beams = 2,repetition_penalty = 2.5,early_stopping = True)\t\n", "\tpreds = [tokenizer.decode(gen_id, skip_special_tokens=True, clean_up_tokenization_space=True) for gen_id in outputs]\n", "\treturn \"\".join(preds)"]}, {"cell_type": "code", "execution_count": null, "id": "59e1530e", "metadata": {}, "outputs": [], "source": ["for i in range(len(data_source)):\n", "    name = names[i]\n", "    doc = data_source[i]\n", "    wc = doc.split(\" \")\n", "    input_len = len(wc)\n", "    req_len = dict_names[name]\n", "    print(str(i) + \": \" + name +  \" - \" + str(input_len) + \" : \" + str(req_len))\n", "    \n", "    abs_summ = generate_summary_gpu(doc,req_len)\n", "    if len(abs_summ.split(\" \")) > req_len:\n", "        abs_summ = abs_summ.split(\" \")\n", "        abs_summ = abs_summ[:req_len]\n", "        abs_summ = \" \".join(abs_summ)\n", "    print(abs_summ)\n", "    print(len((abs_summ.split(\" \"))))\n", "    path = output_path + name\n", "    file = open(path,'w')\n", "    file.write(abs_summ)\n", "    file.close()"]}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "machine_shape": "hm", "name": "pegasus_finetune.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}