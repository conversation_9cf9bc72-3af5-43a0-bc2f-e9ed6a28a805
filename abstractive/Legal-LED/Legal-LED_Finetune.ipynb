{"cells": [{"cell_type": "markdown", "id": "6cf3e374", "metadata": {"id": "6cf3e374"}, "source": ["### Script to finetune Legal-LED model\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "3bbdb36e", "metadata": {}, "outputs": [], "source": ["\n", "import os\n", "os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"1\"\n", "os.environ[\"CUDA_LAUNCH_BLOCKING\"]=\"1\""]}, {"cell_type": "code", "execution_count": null, "id": "fa15c2c3", "metadata": {}, "outputs": [], "source": ["val_files = [] # Add the validation files to be used"]}, {"cell_type": "markdown", "id": "13c64724", "metadata": {}, "source": ["# Fine-tuning Longformer Encoder-Decoder (LED)\n", "\n", "#### Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "c652f875", "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import numpy as np\n", "import pandas as pd\n", "import json\n", "import random\n", "import nltk\n", "nltk.download('punkt')\n", "\n", "from IPython.display import display, HTML\n", "import torch\n", "import datasets\n", "from datasets import load_dataset, load_metric, Dataset\n", "from transformers import AutoTokenizer\n", "from transformers import AutoModelForSeq2SeqLM\n", "from transformers import LEDTokenizer, LEDForConditionalGeneration\n", "from transformers import Seq2SeqTrainer, Seq2SeqTrainingArguments"]}, {"cell_type": "code", "execution_count": null, "id": "5d3b0233", "metadata": {}, "outputs": [], "source": ["model_name = \"nsi319/legal-led-base-16384\"\n", "tokenizer = AutoTokenizer.from_pretrained(model_name)"]}, {"cell_type": "code", "execution_count": null, "id": "ea64378d", "metadata": {}, "outputs": [], "source": ["def getData(tokenizer, dataPath, MAX_DOC_LEN, val = 0):\n", "\tdocumentPath = f'{dataPath}/judgement'\n", "\tsummaryPath = f'{dataPath}/summary'\n", "\tdataset = {'document':[], 'summary':[]}\n", "\tcount = 0\n", "\tfor file in os.listdir(documentPath):\n", "\t\tcount += 1\n", "\t\tif os.stat(f'{documentPath}/{file}').st_size == 0 or os.stat(f'{summaryPath}/{file}').st_size == 0:\n", "\t\t\tcontinue\t\t\t\n", "\t\tdoc_in = open(f'{documentPath}/{file}', 'r', encoding='utf8')\n", "\t\tdoc_lines = [line.strip() for line in doc_in.readlines()]\n", "\t\tsumm_in = open(f'{summaryPath}/{file}', 'r', encoding='utf8')\n", "\t\tsumm_lines = [line.strip() for line in summ_in.readlines()]\n", "\t\tif len(doc_lines) == 0 or len(summ_lines) == 0:\n", "\t\t\tcontinue\n", "\n", "\t\t# print(file, train_files[0], type(file))\n", "\t\tif val == 0 and file not in val_files:\n", "\t\t\tdataset['document'].append(' '.join(doc_lines))\n", "\t\t\tdataset['summary'].append(' '.join(summ_lines))\n", "\t\tif val == 1 and file in val_files:\n", "\t\t\tdataset['document'].append(' '.join(doc_lines))\n", "\t\t\tdataset['summary'].append(' '.join(summ_lines))\n", "\t\n", "\tdf = pd.DataFrame(dataset)\n", "\treturn df"]}, {"cell_type": "code", "execution_count": null, "id": "258135a3", "metadata": {}, "outputs": [], "source": ["exp = 'exp1'\n", "encoder_max_length = 1024*16\n", "decoder_max_length = 1024\n", "batch_size = 1\n", "n_epochs = 3"]}, {"cell_type": "code", "execution_count": null, "id": "b5364ddd", "metadata": {}, "outputs": [], "source": ["dataPath = \"Summary-Data-IN\"\n", "train_df = getData(tokenizer, f'{dataPath}/train-data', encoder_max_length-2)\n", "train_dataset = Dataset.from_pandas(train_df)\n", "val_df = getData(tokenizer, f'{dataPath}/train-data', encoder_max_length-2,1)\n", "val_dataset = Dataset.from_pandas(val_df)"]}, {"cell_type": "markdown", "id": "e80a4ab2", "metadata": {}, "source": ["Preparing and loading the final datasets"]}, {"cell_type": "code", "execution_count": null, "id": "3eb21961", "metadata": {}, "outputs": [], "source": ["def process_data_to_model_inputs(batch):\n", "    # tokenize the inputs and labels\n", "    inputs = tokenizer(\n", "        batch[\"document\"],\n", "        padding=\"max_length\",\n", "        truncation=True,\n", "        max_length=encoder_max_length,\n", "    )\n", "    outputs = tokenizer(\n", "        batch[\"summary\"],\n", "        padding=\"max_length\",\n", "        truncation=True,\n", "        max_length=decoder_max_length,\n", "    )\n", "\n", "    batch[\"input_ids\"] = inputs.input_ids\n", "    batch[\"attention_mask\"] = inputs.attention_mask\n", "\n", "    # create 0 global_attention_mask lists\n", "    batch[\"global_attention_mask\"] = len(batch[\"input_ids\"]) * [\n", "        [0 for _ in range(len(batch[\"input_ids\"][0]))]\n", "    ]\n", "\n", "    # since above lists are references, the following line changes the 0 index for all samples\n", "    batch[\"global_attention_mask\"][0][0] = 1\n", "    batch[\"labels\"] = outputs.input_ids\n", "\n", "    # We have to make sure that the PAD token is ignored\n", "    batch[\"labels\"] = [\n", "        [-100 if token == tokenizer.pad_token_id else token for token in labels]\n", "        for labels in batch[\"labels\"]\n", "    ]\n", "\n", "    return batch\n"]}, {"cell_type": "code", "execution_count": null, "id": "729c0e25", "metadata": {}, "outputs": [], "source": ["# map train data\n", "train_dataset = train_dataset.map(\n", "    process_data_to_model_inputs,\n", "    batched=True,\n", "    batch_size=batch_size,\n", "    remove_columns=[\"document\", \"summary\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "0b95d53e", "metadata": {}, "outputs": [], "source": ["# map val data\n", "val_dataset = val_dataset.map(\n", "    process_data_to_model_inputs,\n", "    batched=True,\n", "    batch_size=batch_size,\n", "    remove_columns=[\"document\", \"summary\"],\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b50ed1fc", "metadata": {}, "outputs": [], "source": ["# set Python list to PyTorch tensor\n", "train_dataset.set_format(\n", "    type=\"torch\",\n", "    columns=[\"input_ids\", \"attention_mask\", \"global_attention_mask\", \"labels\"],\n", ")\n", "\n", "# set Python list to PyTorch tensor\n", "val_dataset.set_format(\n", "    type=\"torch\",\n", "    columns=[\"input_ids\", \"attention_mask\", \"global_attention_mask\", \"labels\"],\n", ")"]}, {"cell_type": "markdown", "id": "c27106e8", "metadata": {}, "source": ["## Training"]}, {"cell_type": "code", "execution_count": null, "id": "2f8f02a4", "metadata": {}, "outputs": [], "source": ["import nltk\n", "nltk.download('punkt')\n", "\n", "# load rouge\n", "rouge = load_metric(\"rouge\")\n", "\n", "def postprocess_text(preds, labels):\n", "\tpreds = [pred.strip() for pred in preds]\n", "\tlabels = [label.strip() for label in labels]\n", "\n", "\t# rougeLSum expects newline after each sentence\n", "\tpreds = [\"\\n\".join(nltk.sent_tokenize(pred)) for pred in preds]\n", "\tlabels = [\"\\n\".join(nltk.sent_tokenize(label)) for label in labels]\n", "\n", "\treturn preds, labels\n", "\n", "\n", "def compute_metrics(pred):\n", "\tlabels_ids = pred.label_ids\n", "\tpred_ids = pred.predictions\n", "\n", "\tpred_str = tokenizer.batch_decode(pred_ids, skip_special_tokens=True)\n", "\tlabels_ids[labels_ids == -100] = tokenizer.pad_token_id\n", "\tlabel_str = tokenizer.batch_decode(labels_ids, skip_special_tokens=True)\n", "\n", "\t# Some simple post-processing\n", "\tpred_str, label_str = postprocess_text(pred_str, label_str)\n", "\t\n", "\tresult = rouge.compute(\n", "\t\tpredictions=pred_str, references=label_str, use_stemmer=True\n", "\t)\n", "\n", "\t# Extract a few results from ROUGE\n", "\tresult = {key: value.mid.fmeasure * 100 for key, value in result.items()}\n", "\n", "\tprediction_lens = [\n", "\t\tnp.count_nonzero(pred != tokenizer.pad_token_id) for pred in pred_ids\n", "\t]\n", "\tresult[\"gen_len\"] = np.mean(prediction_lens)\n", "\tresult = {k: round(v, 4) for k, v in result.items()}\n", "\t\n", "\treturn result"]}, {"cell_type": "markdown", "id": "7753fb3d", "metadata": {}, "source": ["Training Arguments"]}, {"cell_type": "code", "execution_count": null, "id": "37b283c9", "metadata": {}, "outputs": [], "source": ["training_args = Seq2SeqTrainingArguments(\n", "\toutput_dir=f\"results/led/final/{exp}\",\n", "\tnum_train_epochs=n_epochs,\n", "\tper_device_train_batch_size=batch_size,\n", "\tper_device_eval_batch_size=batch_size,\n", "\tfp16=True,\n", "\tevaluation_strategy=\"epoch\",\n", "\tsave_strategy=\"epoch\",\n", "\tload_best_model_at_end=True,\n", "\tmetric_for_best_model=\"eval_rouge2\",\n", "\tgreater_is_better=True,\n", "\twarmup_steps=200,\n", "\tpredict_with_generate=True,\n", "\tlogging_dir=f\"led_logs/final/{exp}\",\n", "\tlogging_steps=50,\n", "    gradient_accumulation_steps=4,\n", "\tsave_total_limit=1 #save only the best model\n", ")"]}, {"cell_type": "markdown", "id": "5b9f0579", "metadata": {}, "source": ["Define Model"]}, {"cell_type": "code", "execution_count": null, "id": "f11e19d8", "metadata": {}, "outputs": [], "source": ["# load model + enable gradient checkpointing & disable cache for checkpointing\n", "led = AutoModelForSeq2SeqLM.from_pretrained(model_name, gradient_checkpointing=True, use_cache=False)\n", "# led.resize_token_embeddings(len(tokenizer))\n", "\n", "# set generate hyperparameters\n", "led.config.num_beams = 2\n", "led.config.max_length = decoder_max_length\n", "led.config.min_length = 256\n", "# led.config.length_penalty = 2.0\n", "led.config.early_stopping = True\n", "led.config.no_repeat_ngram_size = 4\n", "\n", "# instantiate trainer\n", "trainer = Seq2SeqTrainer(\n", "    model=led,\n", "    tokenizer=tokenizer,\n", "    args=training_args,\n", "    compute_metrics=compute_metrics,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=val_dataset,\n", ")\n"]}, {"cell_type": "code", "execution_count": null, "id": "7ade57d4", "metadata": {}, "outputs": [], "source": ["trainer.train()\n", "\n", "#Save the finetuned model\n", "# model_checkpoint_dir = f\"results/led/{exp}/best_model\"\n", "# trainer.save_model(model_checkpoint_dir)\n", "\n", "trainer.save_model(\"./final_model/IN_model\")"]}], "metadata": {"accelerator": "GPU", "colab": {"collapsed_sections": [], "machine_shape": "hm", "name": "pegasus_finetune.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}