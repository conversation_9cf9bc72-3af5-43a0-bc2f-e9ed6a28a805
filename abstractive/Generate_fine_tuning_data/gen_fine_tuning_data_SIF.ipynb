{"cells": [{"cell_type": "markdown", "id": "5dc6c89b", "metadata": {"id": "5dc6c89b"}, "source": ["### Script to generate fine tuning data using SIF similarity method\n", "\n", "Change the datasets variable according to the requirements"]}, {"cell_type": "code", "execution_count": null, "id": "99a24ade", "metadata": {"id": "99a24ade"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN, UK "]}, {"cell_type": "code", "execution_count": null, "id": "c8ac74b6", "metadata": {"id": "c8ac74b6"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import json\n", "import os\n", "import sys\n", "from tqdm import tqdm\n", "sys.path.insert(0, '../')\n", "from utilities import *"]}, {"cell_type": "code", "execution_count": null, "id": "e1ea3042", "metadata": {"id": "e1ea3042"}, "outputs": [], "source": ["#Reading the documents and summaries \n", "names, data_source, data_summary = get_summary_data(dataset, \"train\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))"]}, {"cell_type": "code", "execution_count": null, "id": "9b0c3bff", "metadata": {"id": "9b0c3bff"}, "outputs": [], "source": ["# Loading Model and tokenizer\n", "from transformers import AutoTokenizer\n", "from transformers import  BertModel\n", "tokenizer = AutoTokenizer.from_pretrained(\"bert-base-cased\")\n", "bert_model = BertModel.from_pretrained('bert-base-uncased',output_hidden_states = False).to(\"cuda:1\")\n", "bert_model.eval()"]}, {"cell_type": "code", "execution_count": null, "id": "c14ab500", "metadata": {"id": "c14ab500"}, "outputs": [], "source": ["import numpy as np\n", "from flair.data import Sentence\n", "from sklearn.decomposition import TruncatedSVD\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import csv\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import torch\n", "\n", "#########################################\n", "# HELPER FUNCTIONS\n", "def remove_first_principal_component(X):\n", "    svd = TruncatedSVD(n_components=1, n_iter=7, random_state=0)\n", "    svd.fit(X)\n", "    pc = svd.components_\n", "    XX = X - X.dot(pc.transpose()) * pc\n", "    return XX\n", "\n", "\n", "def load_tsv(f):\n", "    frequencies = {}\n", "    with open(f) as tsv:\n", "        tsv_reader = csv.reader(tsv, delimiter=\"\\t\")\n", "        for row in tsv_reader:\n", "            frequencies[row[0]] = int(row[1])\n", "\n", "    return frequencies\n", "\n", "\n", "def load_frequencies(path):\n", "    return load_tsv(path)\n", "\n", "with open('freq_UK.json', 'r') as fp:\n", "    freqs = json.load(fp)\n", "total_freq = sum(freqs.values())\n", "\n", "#########################################\n", "\n", "def get_sen_encoding(sents):\n", "    '''\n", "    Function to generate encoding for each word in the input list \n", "    input: sents- List of sentences\n", "    return the list of the sentence encoding \n", "    '''\n", "    a = 0.001\n", "    answer = None\n", "    for sent in sents:\n", "        ip =tokenizer(sent, return_tensors='pt', max_length=100, truncation=True, padding='max_length')\n", "        tokens = tokenizer.convert_ids_to_tokens(ip['input_ids'][0])\n", "#         tokens = [i for i in tokens if i in freqs.keys()]\n", "        ip = ip.to(\"cuda:1\")\n", "        bert_output = bert_model(**ip)\n", "        ends = bert_output['last_hidden_state']\n", "        ends = ends.to(\"cpu\")\n", "        tokens_to_encoding = {}\n", "        for i in range(len(tokens)):\n", "            tokens_to_encoding[tokens[i]] = ends[0][i]\n", "        weights = [a / (a + freqs.get(token, 0) / total_freq) for token in tokens]\n", "        \n", "        with torch.no_grad():\n", "            embedding = np.average([tokens_to_encoding[token] for token in tokens], axis=0, weights=weights)\n", "        if answer == None:\n", "            answer = embedding\n", "            answer.resize_(1, 768)\n", "        else:\n", "            embedding.resize_(1, 768)\n", "            answer = torch.cat((answer, embedding),0)\n", "    return answer\n", "def simi_sif(l1, l2):\n", "    print(len(l1))\n", "    print(len(l2))\n", "    l = l1+l2\n", "    sents_encodings = get_sen_encoding(l)\n", "    embeddings = remove_first_principal_component(np.array(sents_encodings))\n", "    similarities=cosine_similarity(sents_encodings)\n", "    result = []\n", "    for i in range(len(l1)):\n", "        vals = similarities[i]\n", "        vals = vals[len(l1):]\n", "        idx = np.argmax(vals)\n", "        result.append(idx)\n", "    print(result)\n", "# simi_sif(split_to_sentences(data_summary[0]), split_to_sentences(data_source[0]))"]}, {"cell_type": "code", "execution_count": null, "id": "a6665614", "metadata": {"id": "a6665614"}, "outputs": [], "source": ["\n", "def similarity_l_l(l1, l2):\n", "    '''\n", "    Function to find the most similar sentence in the document for each sentence in the summary \n", "    input:  l1 - Summary sentences\n", "            l2 - Document sentences\n", "    returns a list of document sentence indexes for each sentence in the summary \n", "    '''\n", "    l = l1+l2\n", "    sents_encodings = get_sen_encoding(l)\n", "    embeddings = remove_first_principal_component(np.array(sents_encodings))\n", "    similarities=cosine_similarity(sents_encodings)\n", "    \n", "    result = []\n", "    for i in range(len(l1)):\n", "        vals = similarities[i]\n", "        vals = vals[len(l1):]\n", "        idx = np.argmax(vals)\n", "        result.append(idx)\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "f574fa42", "metadata": {"id": "f574fa42"}, "outputs": [], "source": ["chunk_summ_word_threshold = 150\n", "\n", "def get_chunks_data_from_docV2(doc, summ):\n", "    '''\n", "    Function to generate chunks along with their summaries \n", "    input:  doc - legal Document\n", "            summ - Gold standard summary\n", "    returns a list of chunks and their summaries \n", "    '''\n", "    sentence_mapping = {}\n", "    doc_sents = split_to_sentences(doc)\n", "    summ_sents = split_to_sentences(summ)\n", "    \n", "    result = (similarity_l_l(summ_sents,doc_sents))\n", "    \n", "    for i in range(len(summ_sents)):\n", "        sentence_mapping[doc_sents[result[i]]] = summ_sents[i]\n", "    \n", "    final_chunks = []\n", "    final_summ = []\n", "    for chunk in nest_sentencesV2(doc, 1024):\n", "        summ = \"\"\n", "        for chunk_sent in chunk:\n", "            if chunk_sent in sentence_mapping:\n", "                summ = summ + sentence_mapping[chunk_sent]\n", "        if len(tokenizer.tokenize(summ)) >= chunk_summ_word_threshold:\n", "            final_chunks.append(\" \".join(chunk))\n", "            final_summ.append(summ)\n", "    return final_chunks, final_summ\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "680336f0", "metadata": {"id": "680336f0"}, "outputs": [], "source": ["#loop to pass every document, generate the fine tuning data and saving in a excel file \n", "import pandas as pd\n", "training_chunks = []\n", "training_summs = []\n", "for i in tqdm(range(len(data_source))):\n", "    cks, summs = get_chunks_data_from_docV2(data_source[i],data_summary[i])\n", "    training_chunks = training_chunks + cks\n", "    training_summs = training_summs + summs\n", "#     print(i, len(training_summs), end = \", \", sep = \" : \")\n", "    if i%100 == 0: \n", "        full = list(zip(training_chunks,training_summs))\n", "        df = pd.DataFrame(full,columns=['data', 'summary'])\n", "        df.to_excel(\"FD_\"+dataset+\"_SIF.xlsx\")\n", "#         break\n", "full = list(zip(training_chunks,training_summs))\n", "df = pd.DataFrame(full,columns=['data', 'summary'])\n", "df.to_excel(\"FD_\"+dataset+\"_SIF.xlsx\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}, "colab": {"name": "gen_fine_tuning_data_SIF.ipynb", "provenance": [], "collapsed_sections": []}}, "nbformat": 4, "nbformat_minor": 5}