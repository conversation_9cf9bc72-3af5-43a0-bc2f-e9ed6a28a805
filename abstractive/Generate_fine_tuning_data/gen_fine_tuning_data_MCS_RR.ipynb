{"cells": [{"cell_type": "markdown", "id": "fa654195", "metadata": {"id": "fa654195"}, "source": ["### Script to generate fine tuning data using MCS similarity method and rhetorical role segmenting\n", "\n", "Change the datasets variable according to the requirements"]}, {"cell_type": "code", "execution_count": null, "id": "160d6034", "metadata": {"id": "160d6034"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN, UK "]}, {"cell_type": "code", "execution_count": null, "id": "b3c21115", "metadata": {"id": "b3c21115"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import os\n", "import sys\n", "from tqdm import tqdm\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "# os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"1\""]}, {"cell_type": "code", "execution_count": null, "id": "6964fafa", "metadata": {"id": "6964fafa"}, "outputs": [], "source": ["#Reading the documents and summaries \n", "names, data_source, data_summary = get_summary_data_rhet_train(dataset)\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))\n"]}, {"cell_type": "code", "execution_count": null, "id": "9c0840ad", "metadata": {"id": "9c0840ad"}, "outputs": [], "source": ["def get_doc_sens_and_labels(doc):\n", "    '''\n", "    function to parse rhetorical role labeled documents\n", "    input: doc - Input document\n", "    output: list of sentences, list of labels, dictionary for every sentence and label\n", "    '''\n", "    sents = []\n", "    labels = []\n", "    dict_sents_labels = {}\n", "    ss = doc.split(\"\\n\")\n", "    for i in ss:\n", "        try:\n", "            spt = i.split(\"\\t\")\n", "            sents.append(spt[0])\n", "            labels.append(spt[1])\n", "            dict_sents_labels[spt[0]] = spt[1] \n", "        except:\n", "            print(i)\n", "    return sents, labels, dict_sents_labels"]}, {"cell_type": "code", "execution_count": null, "id": "f66b036e", "metadata": {"id": "f66b036e"}, "outputs": [], "source": ["def nest_sentencesV2(document_sents,chunk_length):\n", "    '''\n", "    function to chunk the document using document sentences\n", "    input:  document_sents - Input document sentence\n", "            chunk_length - chunk length\n", "    output: list of chunks\n", "    '''\n", "    #modeified v2\n", "    nested = []\n", "    sent = []\n", "    length = 0\n", "    \n", "    for sentence in document_sents:\n", "        length += len((sentence.split(\" \")))\n", "        if length < chunk_length:\n", "            sent.append(sentence)\n", "        else:\n", "            nested.append(sent)\n", "            sent = []\n", "            sent.append(sentence)\n", "            length = 0\n", "    if len(sent)>0:\n", "        nested.append(sent)\n", "    return nested"]}, {"cell_type": "code", "execution_count": null, "id": "7ef30c94", "metadata": {"id": "7ef30c94"}, "outputs": [], "source": ["# Loading the similarity Model \n", "from sentence_transformers import SentenceTransformer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "sbert_model = SentenceTransformer('sentence-transformers/bert-base-nli-mean-tokens').to(\"cuda:1\")"]}, {"cell_type": "code", "execution_count": null, "id": "46f53320", "metadata": {"id": "46f53320"}, "outputs": [], "source": ["def similarity_l_l(l1, l2):\n", "    '''\n", "    Function to find the most similar sentence in the document for each sentence in the summary \n", "    input:  l1 - Summary sentences\n", "            l2 - Document sentences\n", "    returns a list of document sentence indexes for each sentence in the summary \n", "    '''\n", "    document_embeddings = sbert_model.encode(l1+l2)\n", "    similarities=cosine_similarity(document_embeddings)\n", "    \n", "    result = []\n", "    for i in range(len(l1)):\n", "        vals = similarities[i]\n", "        vals = vals[len(l1):]\n", "        idx = np.argmax(vals)\n", "        result.append(idx)\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "09e980ee", "metadata": {"id": "09e980ee"}, "outputs": [], "source": ["def nest_sentencesV3(doc_sents,chunk_length, dict_sents_labels):\n", "    '''\n", "    function to first segment the document using rhetorical roles and then chunk if required\n", "    input:  doc_sents           - Input document sentence\n", "            chunk_length        - chunk length\n", "            dict_sents_labels   - dictionary for every sentence and label\n", "    output: list of chunks\n", "    '''\n", "    s = list(set(dict_sents_labels.values()))\n", "#     print(s)\n", "    all_chunks = []\n", "    \n", "    for label in s:\n", "        doc_sents_withlabels = []\n", "        for sent in doc_sents:\n", "            if sent == '':continue\n", "            if dict_sents_labels[sent] == label:\n", "                doc_sents_withlabels.append(sent)\n", "        chunks = nest_sentencesV2(doc_sents_withlabels, chunk_length)\n", "        \n", "        edited_chunks = []\n", "        for chunk in chunks:\n", "            edited_chunks.append([\"<\" + label + \">\"] + chunk)\n", "        \n", "        all_chunks = all_chunks + edited_chunks\n", "\n", "    return all_chunks    \n"]}, {"cell_type": "code", "execution_count": null, "id": "2891b57f", "metadata": {"id": "2891b57f"}, "outputs": [], "source": ["chunk_summ_word_threshold = 100\n", "\n", "def get_chunks_data_from_docV2(doc, summ):\n", "    '''\n", "    Function to generate chunks along with their summaries \n", "    input:  doc - legal Document\n", "            summ - Gold standard summary\n", "    returns a list of chunks and their summaries \n", "    '''\n", "    sentence_mapping = {}\n", "    \n", "    doc_sents, _, dict_sents_labels = get_doc_sens_and_labels(doc)\n", "#     doc_sents = split_to_sentences(doc)\n", "    summ_sents = split_to_sentences(summ)\n", "    \n", "    result = (similarity_l_l(summ_sents,doc_sents))\n", "    \n", "    for i in range(len(summ_sents)):\n", "        sentence_mapping[doc_sents[result[i]]] = summ_sents[i]\n", "    \n", "    final_chunks = []\n", "    final_summ = []\n", "    for chunk in nest_sentencesV3(doc_sents, 1024, dict_sents_labels):\n", "        summ = \"\"\n", "        for chunk_sent in chunk:\n", "            if chunk_sent in sentence_mapping:\n", "                summ = summ + sentence_mapping[chunk_sent]\n", "        if len((summ.split(\" \"))) >= chunk_summ_word_threshold:\n", "            final_chunks.append(\" \".join(chunk))\n", "            final_summ.append(summ)\n", "    return final_chunks, final_summ\n", "\n", "# cks2, summs2 = get_chunks_data_from_docV2(data_source[0],data_summary[0])\n"]}, {"cell_type": "code", "execution_count": null, "id": "0238b231", "metadata": {"id": "0238b231"}, "outputs": [], "source": ["#loop to pass every document, generate the fine tuning data and saving in a excel file \n", "import pandas as pd\n", "training_chunks = []\n", "training_summs = []\n", "for i in tqdm(range(len(data_source))):\n", "    cks, summs = get_chunks_data_from_docV2(data_source[i],data_summary[names[i]])\n", "    training_chunks = training_chunks + cks\n", "    training_summs = training_summs + summs\n", "#     print(i, len(training_summs), end = \", \", sep = \" : \")\n", "    if i%100 == 0: \n", "        full = list(zip(training_chunks,training_summs))\n", "        df = pd.DataFrame(full,columns=['data', 'summary'])\n", "        df.to_excel(\"FD_\"+ dataset + \"_RR_MCS_7030_BK.xlsx\")\n", "#         break\n", "full = list(zip(training_chunks,training_summs))\n", "df = pd.DataFrame(full,columns=['data', 'summary'])\n", "df.to_excel(\"FD_\"+ dataset +\"_RR_MCS_7030_BK.xlsx\")"]}], "metadata": {"colab": {"collapsed_sections": [], "name": "gen_fine_tuning_data_MCS_RR.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}