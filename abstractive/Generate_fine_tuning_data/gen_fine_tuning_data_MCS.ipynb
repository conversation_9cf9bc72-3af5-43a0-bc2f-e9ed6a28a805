{"cells": [{"cell_type": "markdown", "id": "f1087f67", "metadata": {"id": "f1087f67"}, "source": ["### Script to generate fine tuning data using MCS similarity method\n", "\n", "Change the datasets variable according to the requirements"]}, {"cell_type": "code", "execution_count": null, "id": "a4e43880", "metadata": {"id": "a4e43880"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN, UK "]}, {"cell_type": "code", "execution_count": null, "id": "d5098bbc", "metadata": {"id": "d5098bbc"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import json\n", "from tqdm import tqdm\n", "import os\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "# os.environ[\"CUDA_VISIBLE_DEVICES\"]=\"0\""]}, {"cell_type": "code", "execution_count": null, "id": "e1ea3042", "metadata": {"id": "e1ea3042"}, "outputs": [], "source": ["#Reading the documents and summaries \n", "names, data_source, data_summary = get_summary_data(dataset, \"train\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))"]}, {"cell_type": "code", "execution_count": null, "id": "43bdbed6", "metadata": {"id": "43bdbed6"}, "outputs": [], "source": ["# Loading Model and tokenizer\n", "from sentence_transformers import SentenceTransformer\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "\n", "sbert_model = SentenceTransformer('sentence-transformers/bert-base-nli-mean-tokens').to(\"cuda\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "a6665614", "metadata": {"id": "a6665614"}, "outputs": [], "source": ["def similarity_l_l(l1, l2):\n", "    '''\n", "    Function to find the most similar sentence in the document for each sentence in the summary \n", "    input:  l1 - Summary sentences\n", "            l2 - Document sentences\n", "    returns a list of document sentence indexes for each sentence in the summary \n", "    '''\n", "    document_embeddings = sbert_model.encode(l1+l2)\n", "    similarities=cosine_similarity(document_embeddings)\n", "    \n", "    result = []\n", "    for i in range(len(l1)):\n", "        vals = similarities[i]\n", "        vals = vals[len(l1):]\n", "        idx = np.argmax(vals)\n", "        result.append(idx)\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "f574fa42", "metadata": {"id": "f574fa42"}, "outputs": [], "source": ["def get_chunks_data_from_docV2(doc, summ):\n", "    '''\n", "    Function to generate chunks along with their summaries \n", "    input:  doc - legal Document\n", "            summ - Gold standard summary\n", "    returns a list of chunks and their summaries \n", "    '''\n", "    chunk_summ_word_threshold = 150\n", "    sentence_mapping = {}\n", "    doc_sents = split_to_sentences(doc)\n", "    summ_sents = split_to_sentences(summ)\n", "    \n", "    result = (similarity_l_l(summ_sents,doc_sents))\n", "    \n", "    for i in range(len(summ_sents)):\n", "        sentence_mapping[doc_sents[result[i]]] = summ_sents[i]\n", "    \n", "    final_chunks = []\n", "    final_summ = []\n", "    for chunk in nest_sentencesV2(doc, 1024):\n", "        summ = \"\"\n", "        for chunk_sent in chunk:\n", "            if chunk_sent in sentence_mapping:\n", "                summ = summ + sentence_mapping[chunk_sent]\n", "        if len(summ.split(\" \")) >= chunk_summ_word_threshold:\n", "            final_chunks.append(\" \".join(chunk))\n", "            final_summ.append(summ)\n", "    return final_chunks, final_summ\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "680336f0", "metadata": {"id": "680336f0"}, "outputs": [], "source": ["#loop to pass every document, generate the fine tuning data and saving in a excel file \n", "import pandas as pd\n", "training_chunks = []\n", "training_summs = []\n", "for i in tqdm(range(len(data_source))):\n", "    cks, summs = get_chunks_data_from_docV2(data_source[i],data_summary[i])\n", "    training_chunks = training_chunks + cks\n", "    training_summs = training_summs + summs\n", "#     print(i, len(training_summs), end = \", \", sep = \" : \")\n", "    if i%100 == 0: \n", "        full = list(zip(training_chunks,training_summs))\n", "        df = pd.DataFrame(full,columns=['data', 'summary'])\n", "        df.to_excel(\"FD_\" + dataset + \"_CSM_BK_512.xlsx\")\n", "#         break\n", "full = list(zip(training_chunks,training_summs))\n", "df = pd.DataFrame(full,columns=['data', 'summary'])\n", "df.to_excel(\"FD_\" + dataset + \"_CSM_512.xlsx\")"]}, {"cell_type": "code", "execution_count": null, "id": "7c9b32fb", "metadata": {"id": "7c9b32fb"}, "outputs": [], "source": [""]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.11"}, "colab": {"name": "gen_fine_tuning_data_MCS.ipynb", "provenance": [], "collapsed_sections": []}}, "nbformat": 4, "nbformat_minor": 5}