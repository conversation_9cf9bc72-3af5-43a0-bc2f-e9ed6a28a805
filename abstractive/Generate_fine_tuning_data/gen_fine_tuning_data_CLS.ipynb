{"cells": [{"cell_type": "markdown", "id": "06bdfbbf", "metadata": {"id": "06bdfbbf"}, "source": ["### Script to generate fine tuning data using CLS similarity method\n", "\n", "Change the datasets variable according to the requirements"]}, {"cell_type": "code", "execution_count": null, "id": "6a8faa6c", "metadata": {"id": "6a8faa6c"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN, UK "]}, {"cell_type": "code", "execution_count": null, "id": "605bcef6", "metadata": {"id": "605bcef6"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import json\n", "import os\n", "import sys\n", "from tqdm import tqdm\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "from sklearn.metrics.pairwise import cosine_similarity\n", "import torch"]}, {"cell_type": "code", "execution_count": null, "id": "802a0612", "metadata": {"id": "802a0612"}, "outputs": [], "source": ["#Reading the documents and summaries \n", "names, data_source, data_summary = get_summary_data(dataset, \"train\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))"]}, {"cell_type": "code", "execution_count": null, "id": "9b0c3bff", "metadata": {"id": "9b0c3bff"}, "outputs": [], "source": ["# Loading Model and tokenizer\n", "from transformers import AutoTokenizer\n", "from transformers import  BertModel\n", "tokenizer = AutoTokenizer.from_pretrained(\"bert-base-cased\")\n", "bert_model = BertModel.from_pretrained('bert-base-uncased',output_hidden_states = False).to(\"cuda\")\n", "bert_model.eval()"]}, {"cell_type": "code", "execution_count": null, "id": "c14ab500", "metadata": {"id": "c14ab500"}, "outputs": [], "source": ["def get_sen_encoding(sents):\n", "    '''\n", "    Function to generate encoding for each word in the input list \n", "    input: sents - List of sentences\n", "    returns the list of the sentence encoding \n", "    '''\n", "    a = 0.001\n", "    answer = None\n", "    for sent in sents:\n", "        ip =tokenizer(sent, return_tensors='pt', max_length=150, truncation=True, padding='max_length')\n", "        tokens = tokenizer.convert_ids_to_tokens(ip['input_ids'][0])\n", "        ip = ip.to(\"cuda\")\n", "        bert_output = bert_model(**ip)\n", "        embedding = bert_output['pooler_output'].clone().detach()\n", "        embedding = embedding.to(\"cpu\")\n", "        if answer == None:\n", "            answer = embedding\n", "            answer.resize_(1, 768)\n", "        else:\n", "            embedding.resize_(1, 768)\n", "            answer = torch.cat((answer, embedding),0)\n", "    return answer"]}, {"cell_type": "code", "execution_count": null, "id": "a6665614", "metadata": {"id": "a6665614"}, "outputs": [], "source": ["def similarity_l_l(l1, l2):\n", "    '''\n", "    Function to find the most similar sentence in the document for each sentence in the summary \n", "    input:  l1 - Summary sentences\n", "            l2 - Document sentences\n", "    returns a list of document sentence indexes for each sentence in the summary \n", "    '''\n", "    l = l1+l2\n", "    sents_encodings = get_sen_encoding(l)\n", "    similarities=cosine_similarity(sents_encodings)\n", "    \n", "    result = []\n", "    for i in range(len(l1)):\n", "        vals = similarities[i]\n", "        vals = vals[len(l1):]\n", "        idx = np.argmax(vals)\n", "        result.append(idx)\n", "    return result"]}, {"cell_type": "code", "execution_count": null, "id": "f574fa42", "metadata": {"id": "f574fa42"}, "outputs": [], "source": ["def get_chunks_data_from_docV2(doc, summ):\n", "    '''\n", "    Function to generate chunks along with their summaries \n", "    input:  doc - legal Document\n", "            summ - Gold standard summary\n", "    returns a list of chunks and their summaries \n", "    '''\n", "    chunk_summ_word_threshold = 150\n", "    sentence_mapping = {}\n", "    doc_sents = split_to_sentences(doc)\n", "    summ_sents = split_to_sentences(summ)\n", "    \n", "    result = (similarity_l_l(summ_sents,doc_sents))\n", "    \n", "    for i in range(len(summ_sents)):\n", "        sentence_mapping[doc_sents[result[i]]] = summ_sents[i]\n", "    \n", "    final_chunks = []\n", "    final_summ = []\n", "    for chunk in nest_sentencesV2(doc, 1024):\n", "        summ = \"\"\n", "        for chunk_sent in chunk:\n", "            if chunk_sent in sentence_mapping:\n", "                summ = summ + sentence_mapping[chunk_sent]\n", "        if len(tokenizer.tokenize(summ)) >= chunk_summ_word_threshold:\n", "            final_chunks.append(\" \".join(chunk))\n", "            final_summ.append(summ)\n", "    return final_chunks, final_summ\n"]}, {"cell_type": "code", "execution_count": null, "id": "680336f0", "metadata": {"id": "680336f0"}, "outputs": [], "source": ["#loop to pass every document, generate the fine tuning data and saving in a excel file \n", "import pandas as pd\n", "training_chunks = []\n", "training_summs = []\n", "for i in tqdm(range(len(data_source))):\n", "    cks, summs = get_chunks_data_from_docV2(data_source[i],data_summary[i])\n", "    training_chunks = training_chunks + cks\n", "    training_summs = training_summs + summs\n", "#     print(i, len(training_summs), end = \", \", sep = \" : \")\n", "    if i%100 == 0: \n", "        full = list(zip(training_chunks,training_summs))\n", "        df = pd.DataFrame(full,columns=['data', 'summary'])\n", "        df.to_excel(\"FD_\"+dataset+\"_CLS_BK.xlsx\")\n", "#         break\n", "full = list(zip(training_chunks,training_summs))\n", "df = pd.DataFrame(full,columns=['data', 'summary'])\n", "df.to_excel(\"FD_\"+dataset+\"_CLS.xlsx\")"]}], "metadata": {"colab": {"collapsed_sections": [], "name": "gen_fine_tuning_data_CLS.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}