# Introduction

This repository contains scripts related to *Abstractive Summarization*  algorithms for legal case documents.

| Directory | Description |
| ------------- | ------------- |
| BART based approaches | Scripts to fine-tune BART and generate summaries using BART_RR, BART and BERT-BART method |
| BertSum-Abs | Implementation of BERTSum-abs and helper scripts |
| Generate fine-tuning data | Scripts to generate fine tuning data using CLS, MCS, SIF and MCS_RR methods |
| Legal-LED | Scripts to fine-tune and generate summaries using Legal-LED |
| Legal-Pegasus | Scripts to fine-tune and generate summaries using Pegasus |
| Pointer Generator | Implementation of Pointer-Generator and helper scripts |


# Availability of Trained models

The following models trained on the IN-Abs & UK-Abs datasets, are available at https://zenodo.org/record/7234359#.Y2tShdJByC1

- Legal-LED
- Legal-Pegasus
