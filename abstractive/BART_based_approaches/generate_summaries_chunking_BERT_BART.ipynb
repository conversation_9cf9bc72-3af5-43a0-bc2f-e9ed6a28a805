{"cells": [{"cell_type": "markdown", "id": "8793d0c1", "metadata": {"id": "8793d0c1"}, "source": ["### Script to generate summaries using chunking based BERT_BART method\n", "\n", "Assign the dataset and ouput_folder variable according to requirements.  \n"]}, {"cell_type": "code", "execution_count": null, "id": "b94e8ba9", "metadata": {"id": "b94e8ba9"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN - IN-Abs, UK-UK-Abs, N2-IN-Ext \n", "output_path = \"./IN_BERT_BART/\""]}, {"cell_type": "code", "execution_count": null, "id": "ad7a3c99", "metadata": {"id": "ad7a3c99"}, "outputs": [], "source": ["import sys\n", "from BART_utilities import *\n", "sys.path.insert(0, '../')\n", "import transformers\n", "import pandas as pd\n", "import numpy as np\n", "import glob\n", "import nltk\n", "import torch\n", "from utilities import *\n", "import math\n", "import random\n", "import re\n", "import argparse\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "383b92c3", "metadata": {"id": "383b92c3"}, "outputs": [], "source": ["#Reading the test documents\n", "names, data_source, data_summary = get_summary_data(dataset, \"test\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))"]}, {"cell_type": "code", "execution_count": null, "id": "41e0ecab", "metadata": {"id": "41e0ecab"}, "outputs": [], "source": ["dict_names = get_req_len_dict(dataset, \"test\")   "]}, {"cell_type": "code", "execution_count": null, "id": "ea21bb4b", "metadata": {"id": "ea21bb4b"}, "outputs": [], "source": ["# Loading Model and tokenizer\n", "from transformers import BartTokenizer, BartForConditionalGeneration, AdamW, BartConfig\n", "\n", "tokenizer = BartTokenizer.from_pretrained('facebook/bart-large', add_prefix_space=True)\n", "\n", "model = BartForConditionalGeneration.from_pretrained(\"facebook/bart-large\")"]}, {"cell_type": "markdown", "id": "b7902d06", "metadata": {"id": "b7902d06"}, "source": ["### For using fine tuned model \n", "1. uncomment the 2nd line\n", "2. add the path to the fine tuned model"]}, {"cell_type": "code", "execution_count": null, "id": "37717f34", "metadata": {"id": "37717f34"}, "outputs": [], "source": ["bart_model = LitModel(learning_rate = 2e-5, tokenizer = tokenizer, model = model)\n", "\n", "# bart_model = LitModel.load_from_checkpoint(\"/home/<USER>/HULK/Abhay/models/BART_large_IN_MCS.ckpt\",\n", "#                                       learning_rate = 2e-5, tokenizer = tokenizer, model = model).to(\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "id": "39eae844", "metadata": {"id": "39eae844"}, "outputs": [], "source": ["def generate_summary_gpu(nested_sentences,p=0.2):\n", "  '''\n", "    Function to generate summaries from the list containing chunks of the document\n", "    input:  nested_sentences - chunks\n", "            p - Number of words in summaries per word in the document\n", "    output: document summary\n", "  '''\n", "  device = 'cuda'\n", "  summaries = []\n", "  for nested in nested_sentences:\n", "    l = int(p * len(nested.split(\" \")))\n", "#     print(l)\n", "    input_tokenized = tokenizer.encode(nested, truncation=True, return_tensors='pt')\n", "    input_tokenized = input_tokenized.to(device)\n", "    summary_ids = bart_model.model.to('cuda').generate(input_tokenized,\n", "                                      length_penalty=0.01,\n", "                                      min_length=l-5,\n", "                                      max_length=l+5)\n", "    output = [tokenizer.decode(g, skip_special_tokens=True, clean_up_tokenization_spaces=False) for g in summary_ids]\n", "    summaries.append(output)\n", "  summaries = [sentence for sublist in summaries for sentence in sublist]\n", "  return summaries"]}, {"cell_type": "code", "execution_count": null, "id": "69571a9f", "metadata": {"id": "69571a9f"}, "outputs": [], "source": ["from summarizer import Summarizer\n", "model = Summarizer()"]}, {"cell_type": "code", "execution_count": null, "id": "93b0d593", "metadata": {"id": "93b0d593"}, "outputs": [], "source": ["def summ_doc(doc, req_len):\n", "    '''\n", "    function to generate summaries for a document\n", "    input:  doc - document\n", "            req_len - Required summary length\n", "    output: summary generated using BERT_BART method\n", "    '''\n", "    doc_len = len(doc.split(\" \"))\n", "    r = (5*1024)/doc_len\n", "    if r < 1 and req_len < 5*1024:\n", "        ext_result = model(doc, ratio=r)\n", "    else:\n", "        ext_result = doc\n", "    nested = nest_sentences(ext_result,1024)\n", "    p = float(req_len/len(ext_result.split(\" \")))\n", "    abs_summ = generate_summary_gpu(nested,p)\n", "    \n", "    summ = \" \".join(abs_summ)\n", "    return summ"]}, {"cell_type": "code", "execution_count": null, "id": "1841dc0c", "metadata": {"id": "1841dc0c"}, "outputs": [], "source": ["import os\n", "if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": null, "id": "2bdf4344", "metadata": {"id": "2bdf4344"}, "outputs": [], "source": ["# main loop to generate and save summaries of each document in the test dataset\n", "import pandas as pd\n", "output = []\n", "done = glob.glob(output_path)\n", "done = [i[i.rfind(\"/\")+1:] for i in done]\n", "print(done)\n", "\n", "for i in range(len(data_source)):\n", "    name = names[i]\n", "    if name in done:continue\n", "    doc = data_source[i]\n", "    wc = doc.split(\" \")\n", "    input_len = len(wc)\n", "    req_len = dict_names[name]\n", "    print(str(i) + \": \" + name +  \" - \" + str(input_len) + \" : \" + str(req_len))\n", "    \n", "    abs_summ = summ_doc(doc, req_len)\n", "    path = output_path + name\n", "    file = open(path,'w')\n", "    file.write(abs_summ)\n", "    file.close()\n", "    \n", "print(output)"]}], "metadata": {"colab": {"collapsed_sections": [], "name": "generate_summaries_chunking_BERT_BART.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}