{"cells": [{"cell_type": "markdown", "id": "0e32b71c", "metadata": {"id": "0e32b71c"}, "source": ["### Script to generate summaries using chunking based pre-trained BigBird model\n", "\n", "Assign the dataset and output_path variable according to requirements.  \n"]}, {"cell_type": "code", "execution_count": null, "id": "177e102c", "metadata": {"id": "177e102c"}, "outputs": [], "source": ["dataset = \"IN\" # Options: IN - IN-Abs, UK-UK-Abs, N2-IN-Ext \n", "output_path = \"./IN_BigBird/\""]}, {"cell_type": "code", "execution_count": null, "id": "80b38f7d", "metadata": {"id": "80b38f7d"}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import glob\n", "import sys\n", "sys.path.insert(0, '../')\n", "from utilities import *\n", "import os\n", "import nltk\n", "import torch\n", "from transformers import BigBirdPegasusForConditionalGeneration, AutoTokenizer"]}, {"cell_type": "code", "execution_count": null, "id": "2ed47d74", "metadata": {"id": "2ed47d74"}, "outputs": [], "source": ["if not os.path.exists(output_path):\n", "    os.makedirs(output_path)"]}, {"cell_type": "code", "execution_count": null, "id": "b6fd18d6", "metadata": {"id": "b6fd18d6"}, "outputs": [], "source": ["#Reading the test documents\n", "names, data_source, data_summary = get_summary_data(dataset, \"test\")\n", "print(len(names))\n", "print(len(data_source))\n", "print(len(data_summary))\n", "len_dic = dict_names = get_req_len_dict(dataset, \"test\")   "]}, {"cell_type": "code", "execution_count": null, "id": "2f7b0310", "metadata": {"id": "2f7b0310"}, "outputs": [], "source": ["DATASET_NAME = \"pubmed\"\n", "DEVICE = \"cuda:1\"\n", "CACHE_DIR = DATASET_NAME\n", "MODEL_ID = f\"google/bigbird-pegasus-large-{DATASET_NAME}\"\n", "\n", "tokenizer = AutoTokenizer.from_pretrained(MODEL_ID)\n", "model = BigBirdPegasusForConditionalGeneration.from_pretrained(MODEL_ID).to(DEVICE)\n"]}, {"cell_type": "code", "execution_count": null, "id": "9e8f7c79", "metadata": {"id": "9e8f7c79"}, "outputs": [], "source": ["def get_summ(input, max_l, min_l):\n", "    '''\n", "    Function to generate summaries from the document. This function uses a chunking-based approach.\n", "    input:  nested_sentences - chunks\n", "            p - Number of words in summaries per word in the document\n", "    output: document summary\n", "    '''\n", "    nested = nest_sentences(input, 4096)\n", "    summs = []\n", "    for chunk in nested:\n", "        inputs_dict = tokenizer(chunk, padding=\"max_length\", max_length=4096, return_tensors=\"pt\", truncation=True)\n", "        inputs_dict = {k: inputs_dict[k].to(DEVICE) for k in inputs_dict}\n", "        predicted_abstract_ids = model.generate(**inputs_dict, min_length=min_l, num_beams=5)\n", "        result = tokenizer.decode(predicted_abstract_ids[0], skip_special_tokens=True)\n", "        summs.append(result)\n", "#         print(result)\n", "    summ = '. '.join(summs)\n", "    return summ\n", "    "]}, {"cell_type": "code", "execution_count": null, "id": "36780f7e", "metadata": {"id": "36780f7e"}, "outputs": [], "source": ["# main loop to generate and save summaries of each document in the test dataset\n", "result = []\n", "for i in range(len(data_source)):\n", "    print(str(i) + \" : \" + names[i])\n", "    summ = get_summ(data_source[i], len_dic[names[i]], len_dic[names[i]]-100)\n", "    result.append(summ)\n", "    path = output_path + names[i]\n", "    file = open(path,'w')\n", "    file.write(summ)\n", "    file.close()\n", "#     break\n", "print(result)"]}], "metadata": {"colab": {"collapsed_sections": [], "name": "Pretrained_Bigbird.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.9.13 64-bit (microsoft store)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.13"}, "vscode": {"interpreter": {"hash": "8600bfa2ac79ae324155cec4cf6445cf6a75cd33c56c859c7f5b87c430ce23f5"}}}, "nbformat": 4, "nbformat_minor": 5}